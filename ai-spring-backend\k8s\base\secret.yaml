apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-secret
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "ai-spring-backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
type: Opaque
data:
  # Authentication Secrets (Base64 encoded)
  JWT_SECRET: 
  
  # Database Secrets (DigitalOcean Managed PostgreSQL)
  DB_PASSWORD: QVZOU18wYll6dDBHWmRreTdyblA4S2w3
  
  # SMTP Secrets
  SMTP_USER: 
  SMTP_PASS: 
  
  # OAuth Secrets
  GOOGLE_CLIENT_ID: 
  GOOGLE_CLIENT_SECRET: 
