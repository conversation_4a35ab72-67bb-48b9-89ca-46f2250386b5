apiVersion: v1
kind: Secret
metadata:
  name: RESOURCE_NAME-secret
  namespace: NAMESPACE
  labels:
    app: RESOURCE_NAME
    app.kubernetes.io/name: RESOURCE_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: RESOURCE_NAME
    environment: ENVIRONMENT
    # Display name for human-readable identification
    app.display-name: "APP_NAME"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
type: Opaque
data:
  # Authentication Secrets (Base64 encoded)
  JWT_SECRET: JWT_SECRET_B64
  
  # Database Secrets (DigitalOcean Managed PostgreSQL)
  DB_PASSWORD: QVZOU18wYll6dDBHWmRreTdyblA4S2w3
  
  # SMTP Secrets
  SMTP_USER: SMTP_USER_B64
  SMTP_PASS: SMTP_PASS_B64
  
  # OAuth Secrets
  GOOGLE_CLIENT_ID: GOOGLE_CLIENT_ID_B64
  GOOGLE_CLIENT_SECRET: GOOGLE_CLIENT_SECRET_B64
