apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: dev-overlay
  annotations:
    config.kubernetes.io/local-config: "true"

# Reference to base configuration
resources:
- ../../base

# Environment-specific namespace
namespace: ai-spring-backend-dev

# Environment-specific name prefix
namePrefix: ""

# Common labels for dev environment
commonLabels:
  environment: dev
  deployment.tier: dev

# Common annotations for dev environment
commonAnnotations:
  argocd.argoproj.io/sync-options: "CreateNamespace=true"
  deployment.environment: "dev"

# Dev-specific image configuration
images:
- name: registry.digitalocean.com/doks-registry/ai-spring-backend
  newTag: latest

# ConfigMap generator for environment-specific variables
configMapGenerator:
- name: kustomize-vars
  options:
    disableNameSuffixHash: true
  literals:
  - API_URL=http://ai-spring-backend-service:8080
  - APPLICATION_TYPE=springboot-backend
  - APP_NAME=ai-spring-backend
  - APP_TYPE=springboot-backend
  - APP_URL=http://ai-spring-backend.dev.local
  - APP_VERSION=1.0.0
  - CLUSTER_ID=6be4e15d-52f9-431d-84ec-ec8cad0dff2d
  - CLUSTER_NAME=doks-target-cluster
  - CLUSTER_SERVER=https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
  - COMMIT_SHA=5b46ee6b7c70a1df85c503e2bc8baca79add2237
  - CONTAINER_IMAGE=registry.digitalocean.com/doks-registry/ai-spring-backend:latest
  - CONTAINER_PORT=8080
  - CORS_ORIGINS=http://ai-spring-backend.dev.local,http://localhost:3000,http://localhost:3001
  - CPU_LIMIT=500m
  - CPU_REQUEST=100m
  - DB_HOST=private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com
  - DB_NAME=spring_dev_db
  - DB_PASSWORD=AVNS_0bYzt0GZdky7rnP8Kl7
  - DB_PASSWORD_B64=QVZOU18wYll6dDBHWmRreTdyblA4S2w3
  - DB_PORT=25060
  - DB_USER=spring_dev_user
  - DB_USER_B64=c3ByaW5nX2Rldl91c2Vy
  - DB_SSL_MODE=require
  - DOCKER_IMAGE=registry.digitalocean.com/doks-registry/ai-spring-backend
  - DOCKER_TAG=latest
  - ENABLE_DATABASE=false
  - ENABLE_INGRESS=false
  - ENABLE_PVC=false
  - ENVIRONMENT=dev
  - GOOGLE_REDIRECT_URI=http://ai-spring-backend.dev.local/auth/google/callback
  - HEALTH_CHECK_PATH=/actuator/health
  - HEALTH_CHECK_PORT=8080
  - HEALTH_CHECK_SCHEME=HTTP
  - IMAGE_PULL_POLICY=Always
  - INGRESS_PATH=/
  - IS_BACKEND=true
  - JAVA_XMS=256m
  - JAVA_XMX=512m
  - JWT_EXPIRATION=24h
  - LIVENESS_PROBE_FAILURE_THRESHOLD=3
  - LIVENESS_PROBE_INITIAL_DELAY=30
  - LIVENESS_PROBE_PERIOD=30
  - LIVENESS_PROBE_SUCCESS_THRESHOLD=1
  - LIVENESS_PROBE_TIMEOUT=10
  - MEMORY_LIMIT=512Mi
  - MEMORY_REQUEST=256Mi
  - NAMESPACE=ai-spring-backend-dev
  - OAUTH_REDIRECT_URIS=http://ai-spring-backend.dev.local/auth/google/callback
  - OAUTH_SCOPES=openid,profile,email
  - PROJECT_ID=ai-spring-backend
  - PUBLIC_URL=http://ai-spring-backend.dev.local
  - PVC_SIZE=1Gi
  - READINESS_PROBE_FAILURE_THRESHOLD=3
  - READINESS_PROBE_INITIAL_DELAY=10
  - READINESS_PROBE_PERIOD=10
  - READINESS_PROBE_SUCCESS_THRESHOLD=1
  - READINESS_PROBE_TIMEOUT=5
  - REPLICAS=1
  - REPLICAS_DEFAULT=2
  - RESOURCE_NAME=ai-spring-backend
  - ROLLING_UPDATE_MAX_SURGE=50%
  - ROLLING_UPDATE_MAX_UNAVAILABLE=50%
  - ROLLING_UPDATE_PROGRESS_DEADLINE=120
  - ROLLING_UPDATE_REVISION_HISTORY=3
  - SMTP_FROM=<EMAIL>
  - SMTP_HOST=smtp.gmail.com
  - SMTP_PORT=587
  - SOURCE_BRANCH=25/merge
  - SOURCE_REPO=ChidhagniConsulting/ai-spring-backend
  - STORAGE_SIZE=5Gi
  - TERMINATION_GRACE_PERIOD=30
  - SERVICE_NAME=ai-spring-backend-service
  - SERVICE_NAMESPACE=ai-spring-backend-dev
  - SERVICE_PORT=8080
  - SERVICE_TYPE=springboot-backend
  - SERVICE_HEALTH_PATH=/actuator/health

# Environment-specific patches
patches:
- path: deployment-patch.yaml
  target:
    kind: Deployment
    name: ai-spring-backend
- path: resource-patch.yaml
  target:
    kind: ResourceQuota
    name: ai-spring-backend-resource-quota
- path: security-patch.yaml
  target:
    kind: Deployment
    name: ai-spring-backend

# Remove HPA in development (disabled)
patchesJson6902:
- target:
    version: v2
    kind: HorizontalPodAutoscaler
    name: ai-spring-backend-hpa
  path: disable-hpa-patch.yaml

# Remove Network Policy in development (disabled)
- target:
    version: v1
    kind: NetworkPolicy
    name: ai-spring-backend-network-policy
  path: disable-network-policy-patch.yaml